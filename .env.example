# Confluence MCP Server Configuration

# Required: Confluence API Token
# Get from: https://id.atlassian.com/manage/api-tokens
CONFLUENCE_API_TOKEN=your_api_token_here

# Required: Confluence Base URL
# Format: https://your-domain.atlassian.net/wiki
CONFLUENCE_BASE_URL=https://your-domain.atlassian.net/wiki

# Optional: MCP Transport Method
# Options: stdio (default), sse, streamable-http
MCP_TRANSPORT=stdio

# Optional: HTTP Server Configuration (for sse and streamable-http only)
# Default port: 3000
MCP_PORT=3000

# Default host: localhost
MCP_HOST=localhost
